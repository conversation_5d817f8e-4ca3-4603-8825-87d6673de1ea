console.log('Starting test script...');

const mongoose = require('mongoose');
require('dotenv').config();

console.log('Environment loaded');
console.log('MONGO_URI:', process.env.MONGO_URI);

async function testDB() {
  try {
    console.log('Attempting to connect to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/traksong');
    console.log('Connected to MongoDB!');
    
    const Track = require('./models/Track.model');
    console.log('Track model loaded');
    
    const count = await Track.countDocuments();
    console.log('Total tracks:', count);
    
    const tracks = await Track.find({}).limit(3);
    console.log('First 3 tracks:');
    tracks.forEach((track, index) => {
      console.log(`${index + 1}. ${track.title} - ${track.filePath}`);
    });
    
    await mongoose.disconnect();
    console.log('Disconnected');
  } catch (error) {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

testDB();
