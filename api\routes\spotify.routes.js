const express = require('express');
const router = express.Router();
const spotifyService = require('../services/spotifyService');
const { authenticate, authorizeStore } = require('../middleware/auth.middleware');
const { auditActions } = require('../middleware/audit.middleware');

// Get Spotify authorization URL
router.get('/auth-url', authenticate, async (req, res) => {
  try {
    const { scopes } = req.query;
    const scopeArray = scopes ? scopes.split(',') : [];
    
    const authUrl = spotifyService.generateAuthUrl(req.user.id, scopeArray);
    
    res.json({
      authUrl,
      message: 'Redirect user to this URL to authorize Spotify access'
    });
  } catch (error) {
    console.error('Failed to generate Spotify auth URL:', error);
    res.status(500).json({ error: 'Failed to generate authorization URL' });
  }
});

// Handle OAuth callback
router.post('/callback', authenticate, async (req, res) => {
  try {
    const { code, state, error } = req.body;
    console.log('Spotify callback received:', {
      code: code ? 'present' : 'missing',
      state: state ? 'present' : 'missing',
      error: error || 'none',
      userId: req.user.id
    });

    if (error) {
      console.log('Spotify authorization error:', error);
      return res.status(400).json({
        error: 'Spotify authorization denied',
        details: error
      });
    }

    if (!code || !state) {
      console.log('Missing required parameters:', { code: !!code, state: !!state });
      return res.status(400).json({
        error: 'Missing authorization code or state'
      });
    }

    // Verify state contains user ID
    const [, stateUserId] = state.split(':');
    if (stateUserId !== req.user.id) {
      return res.status(400).json({ 
        error: 'Invalid state parameter' 
      });
    }

    const result = await spotifyService.exchangeCodeForToken(code, state);
    
    // Log the connection for audit trail
    await auditActions.spotifyConnection(
      req.user.id,
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      'connected'
    );

    res.json({
      success: true,
      message: 'Spotify account connected successfully',
      user: result.user,
      expiresIn: result.expiresIn
    });
  } catch (error) {
    console.error('Spotify callback failed:', error);
    res.status(500).json({ 
      error: 'Failed to connect Spotify account',
      details: error.message 
    });
  }
});

// Get integration status
router.get('/status', authenticate, async (req, res) => {
  try {
    const status = await spotifyService.getIntegrationStatus(req.user.id);
    res.json(status);
  } catch (error) {
    console.error('Failed to get Spotify status:', error);
    res.status(500).json({ error: 'Failed to get integration status' });
  }
});

// Get current playback state
router.get('/playback', authenticate, async (req, res) => {
  try {
    const playback = await spotifyService.getCurrentPlayback(req.user.id);
    res.json(playback || { message: 'No active playback' });
  } catch (error) {
    console.error('Failed to get playback state:', error);
    res.status(500).json({ error: 'Failed to get playback state' });
  }
});

// Get currently playing track
router.get('/currently-playing', authenticate, async (req, res) => {
  try {
    const track = await spotifyService.getCurrentlyPlaying(req.user.id);
    res.json(track || { message: 'Nothing currently playing' });
  } catch (error) {
    console.error('Failed to get currently playing:', error);
    res.status(500).json({ error: 'Failed to get currently playing track' });
  }
});

// Get user's devices
router.get('/devices', authenticate, async (req, res) => {
  try {
    const devices = await spotifyService.getDevices(req.user.id);
    res.json(devices);
  } catch (error) {
    console.error('Failed to get devices:', error);
    res.status(500).json({ error: 'Failed to get devices' });
  }
});

// Control playback
router.post('/playback/:action', authenticate, async (req, res) => {
  try {
    const { action } = req.params;
    const data = req.body;

    const validActions = ['play', 'pause', 'next', 'previous', 'seek', 'volume', 'shuffle', 'repeat'];
    if (!validActions.includes(action)) {
      return res.status(400).json({ error: 'Invalid playback action' });
    }

    await spotifyService.controlPlayback(req.user.id, action, data);
    
    // Log playback control for audit
    await auditActions.spotifyPlaybackControl(
      req.user.id,
      action,
      data,
      req.ip || 'unknown'
    );

    res.json({ 
      success: true, 
      message: `Playback ${action} executed successfully` 
    });
  } catch (error) {
    console.error(`Failed to ${req.params.action} playback:`, error);
    res.status(500).json({ 
      error: `Failed to ${req.params.action} playback`,
      details: error.message 
    });
  }
});

// Log current track for compliance
router.post('/log-current', authenticate, async (req, res) => {
  try {
    if (!req.user.storeId) {
      return res.status(400).json({ error: 'Store ID required for logging' });
    }

    const playHistory = await spotifyService.logCurrentTrack(req.user.id, req.user.storeId);
    
    if (!playHistory) {
      return res.json({ message: 'No track currently playing to log' });
    }

    res.json({
      success: true,
      message: 'Track logged successfully',
      playHistory: {
        id: playHistory._id,
        trackTitle: playHistory.spotifyData?.trackId,
        startTime: playHistory.startTime,
        source: 'spotify'
      }
    });
  } catch (error) {
    console.error('Failed to log current track:', error);
    res.status(500).json({ 
      error: 'Failed to log current track',
      details: error.message 
    });
  }
});

// Sync recently played tracks
router.post('/sync-recent', authenticate, async (req, res) => {
  try {
    if (!req.user.storeId) {
      return res.status(400).json({ error: 'Store ID required for syncing' });
    }

    const syncedTracks = await spotifyService.syncRecentlyPlayed(req.user.id, req.user.storeId);
    
    res.json({
      success: true,
      message: `Synced ${syncedTracks.length} tracks`,
      syncedCount: syncedTracks.length,
      tracks: syncedTracks.map(track => ({
        id: track._id,
        trackTitle: track.spotifyData?.trackId,
        startTime: track.startTime
      }))
    });
  } catch (error) {
    console.error('Failed to sync recent tracks:', error);
    res.status(500).json({ 
      error: 'Failed to sync recent tracks',
      details: error.message 
    });
  }
});

// Get recently played tracks
router.get('/recent', authenticate, async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const recentTracks = await spotifyService.getRecentlyPlayed(req.user.id, parseInt(limit));
    res.json(recentTracks);
  } catch (error) {
    console.error('Failed to get recent tracks:', error);
    res.status(500).json({ error: 'Failed to get recent tracks' });
  }
});

// Disconnect Spotify
router.post('/disconnect', authenticate, async (req, res) => {
  try {
    const result = await spotifyService.disconnectSpotify(req.user.id);
    
    // Log disconnection for audit trail
    await auditActions.spotifyConnection(
      req.user.id,
      req.ip || 'unknown',
      req.get('User-Agent') || 'unknown',
      'disconnected'
    );

    res.json(result);
  } catch (error) {
    console.error('Failed to disconnect Spotify:', error);
    res.status(500).json({ 
      error: 'Failed to disconnect Spotify',
      details: error.message 
    });
  }
});

// Refresh token manually
router.post('/refresh-token', authenticate, async (req, res) => {
  try {
    const newToken = await spotifyService.refreshAccessToken(req.user.id);
    res.json({ 
      success: true, 
      message: 'Token refreshed successfully',
      hasValidToken: true
    });
  } catch (error) {
    console.error('Failed to refresh token:', error);
    res.status(500).json({ 
      error: 'Failed to refresh token',
      details: error.message 
    });
  }
});

module.exports = router;
