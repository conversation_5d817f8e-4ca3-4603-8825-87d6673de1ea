import { Howl } from 'howler';
import { historyService } from './api';
import activityLogger from './activityLogger';

class AudioService {
  constructor() {
    this.currentSound = null;
    this.playlist = [];
    this.originalPlaylist = []; // Keep original order for shuffle
    this.currentIndex = 0;
    this.isPlaying = false;
    this.startTime = null;
    this.storeId = null;
    this.deviceId = 'web-player-' + Math.random().toString(36).substr(2, 9);
    this.offlineLogs = JSON.parse(localStorage.getItem('offlineLogs') || '[]');
    this.listeners = [];
    this.shuffle = false;
    this.repeat = false;
    this.volume = parseFloat(localStorage.getItem('storeVolume') || '0.75'); // Default 75%
    this.progressInterval = null;
    this.currentTime = 0;
    this.duration = 0;
    this.currentPlaylistId = null;
    this.currentPlaylistName = null;
    this.trackEndedNaturally = false; // Flag to prevent double logging

    // Scheduled playlist state preservation
    this.scheduledPlaylistState = null; // Stores paused scheduled playlist state
    this.isScheduledPlaylist = false; // Flag to identify if current playlist is scheduled
    this.interruptionMode = null; // 'radio' or 'single_track' or null
  }

  setStoreId(storeId) {
    this.storeId = storeId;
    console.log('Audio service store ID set to:', storeId);
    activityLogger.setStoreId(storeId);
  }

  generateSessionId() {
    return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  validateTrack(track) {
    if (!track) {
      console.warn('Track is null or undefined');
      return false;
    }

    const requiredFields = ['title', 'artist', 'filePath'];
    const missingFields = requiredFields.filter(field => !track[field]);

    if (missingFields.length > 0) {
      console.warn(`Track missing required fields: ${missingFields.join(', ')}`, track);
      return false;
    }

    // Check if track is marked as invalid from backend validation
    if (track.isValid === false) {
      console.warn('Track marked as invalid:', track.validationErrors);
      return false;
    }

    return true;
  }

  buildAudioUrl(filePath) {
    if (!filePath) {
      throw new Error('File path is required');
    }

    // Get base URL from environment or default to localhost
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';
    console.log('AudioService: Base URL from env:', baseUrl);

    // Extract filename from path
    const filename = filePath.split('/').pop();
    console.log('AudioService: Extracted filename:', filename);

    // Use the audio proxy endpoint for ngrok compatibility
    const finalUrl = `${baseUrl}/audio/${filename}`;
    console.log('AudioService: Final audio URL:', finalUrl);

    return finalUrl;
  }

  // Event subscription methods
  subscribe(callback) {
    this.listeners.push(callback);
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  notifyListeners(additionalData = {}) {
    const state = {
      currentTrack: this.getCurrentTrack(),
      isPlaying: this.isPlaying,
      currentIndex: this.currentIndex,
      playlist: this.playlist,
      shuffle: this.shuffle,
      repeat: this.repeat,
      volume: this.volume,
      currentTime: this.currentTime,
      duration: this.duration,
      progress: this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0,
      ...additionalData
    };
    this.listeners.forEach(callback => callback(state));
  }

  loadPlaylist(tracks, playlistInfo = null, isScheduled = false) {
    this.playlist = tracks;
    this.originalPlaylist = [...tracks]; // Keep original order
    this.currentIndex = 0;
    this.currentPlaylistId = playlistInfo?._id || null;
    this.currentPlaylistName = playlistInfo?.name || null;
    this.isScheduledPlaylist = isScheduled;
    this.stop();
    this.notifyListeners();
  }

  loadTrack(track) {
    // Save current scheduled playlist state if switching from scheduled playlist
    if (this.isScheduledPlaylist && this.playlist.length > 0) {
      this.saveScheduledPlaylistState();
    }

    // Load a single track for preview
    this.playlist = [track];
    this.originalPlaylist = [track];
    this.currentIndex = 0;
    this.currentPlaylistId = null;
    this.currentPlaylistName = null;
    this.isScheduledPlaylist = false;
    this.interruptionMode = 'single_track';
    this.notifyListeners();
  }

  setCurrentPlaylistInfo(playlistId, playlistName) {
    this.currentPlaylistId = playlistId;
    this.currentPlaylistName = playlistName;
    this.notifyListeners();
  }

  startProgressTracking() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }

    this.progressInterval = setInterval(() => {
      if (this.currentSound && this.isPlaying) {
        this.currentTime = this.currentSound.seek() || 0;
        this.duration = this.currentSound.duration() || 0;
        this.notifyListeners();
      }
    }, 1000); // Update every second
  }

  stopProgressTracking() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }

  async play(index = null) {
    console.log('AudioService: play called', { index, playlistLength: this.playlist.length });

    if (index !== null) {
      this.currentIndex = index;
    }

    if (this.playlist.length === 0) {
      console.warn('AudioService: Cannot play - playlist is empty');
      return;
    }

    const track = this.playlist[this.currentIndex];
    if (!track) {
      console.warn('AudioService: Cannot play - no track at current index', this.currentIndex);
      return;
    }

    console.log('AudioService: Attempting to play track', {
      title: track.title,
      artist: track.artist,
      filePath: track.filePath
    });

    // Validate track before attempting to play
    if (!this.validateTrack(track)) {
      console.error('AudioService: Invalid track data:', track);
      this.next(); // Skip to next track
      return;
    }

    // Ensure radio is stopped before starting music
    try {
      const radioServiceModule = await import('./radioService');
      const radioService = radioServiceModule.radioService;
      if (radioService && radioService.getIsPlaying()) {
        console.log('AudioService: Force stopping radio before starting music');
        radioService.forceStop();
        // Give a longer delay to ensure radio stops completely
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error) {
      console.warn('AudioService: Could not access radioService:', error);
    }

    this.stop();

    // Build dynamic audio URL
    let audioUrl;
    try {
      audioUrl = this.buildAudioUrl(track.filePath);
      console.log('AudioService: Built audio URL:', audioUrl);
    } catch (error) {
      console.error('AudioService: Failed to build audio URL:', error);
      this.next();
      return;
    }

    this.currentSound = new Howl({
      src: [audioUrl],
      html5: true,
      volume: this.volume,
      onload: () => {
        console.log('AudioService: Track loaded successfully');
      },
      onplay: () => {
        console.log('AudioService: Track started playing');
        this.isPlaying = true;
        this.startTime = new Date();
        this.duration = this.currentSound.duration() || 0;
        this.trackEndedNaturally = false; // Reset flag when starting new track
        this.startProgressTracking();
        console.log(`Playing: ${track.title} by ${track.artist}`);

        // Log music player start activity
        activityLogger.logMusicPlayerStart({
          ...track,
          position: this.currentIndex,
          isShuffled: this.shuffle,
          isRepeating: this.repeat,
          volume: this.volume
        }, {
          _id: this.currentPlaylistId,
          name: this.currentPlaylistName
        });

        this.notifyListeners();
      },
      onend: () => {
        console.log('AudioService: Track ended naturally');
        // Mark that track ended naturally to prevent double logging
        this.trackEndedNaturally = true;

        // Log music player stop activity when track ends naturally
        if (this.isPlaying && this.startTime) {
          const duration = (new Date() - this.startTime) / 1000;
          activityLogger.logMusicPlayerStop({
            ...track,
            volume: this.volume
          }, duration);

          // Log the play to history
          this.logPlay(track);
        }

        // Notify listeners that a track finished playing
        this.notifyListeners({ trackFinished: true });
        this.next();
      },
      onerror: (id, error) => {
        console.error('AudioService: Audio playback error:', { id, error, audioUrl });
        this.next();
      },
      onloaderror: (id, error) => {
        console.error('AudioService: Audio load error:', { id, error, audioUrl });
        this.next();
      }
    });

    console.log('AudioService: Starting playback...');
    this.currentSound.play();
  }

  pause() {
    if (this.currentSound) {
      this.currentSound.pause();
      this.isPlaying = false;
      this.stopProgressTracking();
      this.notifyListeners();
    }
  }

  resume() {
    if (this.currentSound) {
      this.currentSound.play();
      this.isPlaying = true;
      this.startProgressTracking();
      this.notifyListeners();
    }
  }

  stop() {
    if (this.currentSound) {
      if (this.isPlaying && this.startTime && !this.trackEndedNaturally) {
        const track = this.playlist[this.currentIndex];
        if (track) {
          // Only log if this is a manual stop (not from onend event)
          // Log music player stop activity for manual stops
          const playDuration = (new Date() - this.startTime) / 1000;
          activityLogger.logMusicPlayerStop({
            ...track,
            volume: this.volume
          }, playDuration);

          // Log the play to history for manual stops
          this.logPlay(track);
        }
      }
      this.currentSound.stop();
      this.currentSound = null;
      this.isPlaying = false;
      this.startTime = null;
      this.currentTime = 0;
      this.duration = 0;
      this.trackEndedNaturally = false; // Reset flag
      this.stopProgressTracking();
      this.notifyListeners();
    }
  }

  next() {
    if (this.currentIndex < this.playlist.length - 1) {
      this.currentIndex++;
      this.play();
    } else if (this.repeat) {
      // If repeat is enabled, loop back to start
      this.currentIndex = 0;
      this.play();
    } else {
      // Playlist ended, check if we should resume scheduled playlist
      this.handlePlaylistEnd();
    }
  }

  async handlePlaylistEnd() {
    // If this was an interruption (single track or non-scheduled playlist), try to resume scheduled playlist
    if (this.interruptionMode && this.hasScheduledPlaylistState()) {
      console.log('Playlist ended, attempting to resume scheduled playlist...');

      // Import scheduleService dynamically to avoid circular dependency
      const { scheduleService } = await import('./scheduleService');
      const resumed = await scheduleService.resumeScheduledPlaylist();

      if (resumed) {
        console.log('Successfully resumed scheduled playlist');
        return;
      }
    }

    // For manual tracks, if we reach the end of the playlist and no scheduled playlists,
    // the similar tracks should already be loaded in the playlist, so just stop
    console.log('Playlist ended, no more tracks to play');

    // No scheduled playlist to resume, just stop
    this.stop();
  }



  previous() {
    if (this.currentIndex > 0) {
      this.currentIndex--;
      this.play();
    }
  }

  async logPlay(track) {
    console.log('logPlay called with:', {
      trackTitle: track?.title,
      storeId: this.storeId,
      hasStartTime: !!this.startTime
    });

    if (!this.startTime || !this.storeId) {
      console.warn('Cannot log play - missing startTime or storeId:', {
        startTime: this.startTime,
        storeId: this.storeId
      });
      return;
    }

    const endTime = new Date();
    const durationPlayed = (endTime - this.startTime) / 1000;

    const playData = {
      storeId: this.storeId,
      deviceId: this.deviceId,
      trackId: track._id,
      playlistId: this.currentPlaylistId,
      startTime: this.startTime,
      endTime: endTime,
      durationPlayed: Math.round(durationPlayed),
      // Include track duration for offline sync compatibility
      totalTrackDuration: track.duration || this.duration || 0,
      // Add metadata for better tracking and compliance
      metadata: {
        playlistName: this.currentPlaylistName,
        trackTitle: track.title,
        trackArtist: track.artist,
        volume: this.volume,
        sourceType: 'music_player',
        deviceType: 'web_player',
        audioQuality: 'standard',
        // Add shuffle and repeat state for compliance tracking
        isShuffled: this.shuffle,
        isRepeating: this.repeat,
        playSequence: this.currentIndex,
        sessionId: this.generateSessionId()
      },
      // Add compliance information
      compliance: {
        reportedToSAMRO: false,
        reportedToSAMPRA: false,
        reportedToRISA: false
      },
      // Add audit information
      auditInfo: {
        sessionId: this.generateSessionId(),
        playSequence: this.currentIndex,
        repeatCount: this.repeat ? 1 : 0,
        technicalIssues: []
      }
    };

    try {
      console.log('Logging play to server:', {
        trackTitle: track.title,
        storeId: this.storeId,
        durationPlayed: Math.round(durationPlayed)
      });
      await historyService.log(playData);
      console.log('Play logged successfully');
    } catch (error) {
      console.error('Failed to log play, storing offline:', error);
      this.offlineLogs.push(playData);
      localStorage.setItem('offlineLogs', JSON.stringify(this.offlineLogs));
    }
  }

  async syncOfflineLogs() {
    if (this.offlineLogs.length === 0) return;

    console.log(`Attempting to sync ${this.offlineLogs.length} offline logs...`);

    try {
      // Send logs in the correct format expected by the API
      const response = await historyService.syncOffline({ logs: this.offlineLogs });

      // Handle the response
      const result = response.data;
      if (result.success) {
        console.log(`Successfully synced ${result.synced} of ${result.total} offline logs`);

        // Log any errors that occurred during sync
        if (result.errors && result.errors.length > 0) {
          console.warn(`${result.errors.length} logs had errors during sync:`, result.errors);
        }

        // Clear offline logs on successful sync
        this.offlineLogs = [];
        localStorage.removeItem('offlineLogs');
      } else {
        console.error('Sync failed:', result.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Failed to sync offline logs:', error);

      // Log detailed error information
      if (error.response?.data) {
        console.error('Server response:', error.response.data);
      }

      // If it's a validation error, clear the problematic logs
      if (error.response?.status === 400) {
        console.warn('Clearing invalid offline logs due to validation error');
        this.offlineLogs = [];
        localStorage.removeItem('offlineLogs');
      }
    }
  }

  // Clear offline logs (useful for debugging)
  clearOfflineLogs() {
    this.offlineLogs = [];
    localStorage.removeItem('offlineLogs');
    console.log('Offline logs cleared');
  }

  // Get offline logs count
  getOfflineLogsCount() {
    return this.offlineLogs.length;
  }

  getCurrentTrack() {
    return this.playlist[this.currentIndex] || null;
  }

  getPlaylist() {
    return this.playlist;
  }

  getCurrentPlaylistInfo() {
    return {
      id: this.currentPlaylistId,
      name: this.currentPlaylistName,
      isScheduled: this.isScheduledPlaylist
    };
  }

  getCurrentIndex() {
    return this.currentIndex;
  }

  getIsPlaying() {
    return this.isPlaying;
  }

  toggleShuffle() {
    this.shuffle = !this.shuffle;

    if (this.shuffle) {
      // Shuffle the playlist while keeping current track at current position
      const currentTrack = this.playlist[this.currentIndex];
      const otherTracks = this.playlist.filter((_, index) => index !== this.currentIndex);

      // Fisher-Yates shuffle for other tracks
      for (let i = otherTracks.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [otherTracks[i], otherTracks[j]] = [otherTracks[j], otherTracks[i]];
      }

      // Reconstruct playlist with current track at index 0
      this.playlist = [currentTrack, ...otherTracks];
      this.currentIndex = 0;
    } else {
      // Restore original order
      if (this.originalPlaylist) {
        const currentTrack = this.playlist[this.currentIndex];
        this.playlist = [...this.originalPlaylist];
        // Find the current track in the original playlist
        this.currentIndex = this.playlist.findIndex(track => track._id === currentTrack._id);
        if (this.currentIndex === -1) this.currentIndex = 0;
      }
    }

    this.notifyListeners();
  }

  toggleRepeat() {
    this.repeat = !this.repeat;
    this.notifyListeners();
  }

  getShuffle() {
    return this.shuffle;
  }

  getRepeat() {
    return this.repeat;
  }

  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  setVolume(volume) {
    // Ensure volume is between 0 and 1
    this.volume = Math.max(0, Math.min(1, volume));

    // Update current playing sound
    if (this.currentSound) {
      this.currentSound.volume(this.volume);
    }

    // Save to localStorage
    localStorage.setItem('storeVolume', this.volume.toString());

    // Notify listeners
    this.notifyListeners();
  }

  getVolume() {
    return this.volume;
  }

  getVolumePercentage() {
    return Math.round(this.volume * 100);
  }

  seek(percentage) {
    if (this.currentSound && this.duration > 0) {
      const seekTime = (percentage / 100) * this.duration;
      this.currentSound.seek(seekTime);
      this.currentTime = seekTime;
      this.notifyListeners();
    }
  }

  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  getCurrentTime() {
    return this.currentTime;
  }

  getDuration() {
    return this.duration;
  }

  getProgress() {
    return this.duration > 0 ? (this.currentTime / this.duration) * 100 : 0;
  }

  // Scheduled playlist state management methods
  saveScheduledPlaylistState() {
    if (!this.isScheduledPlaylist) return;

    this.scheduledPlaylistState = {
      playlist: [...this.playlist],
      originalPlaylist: [...this.originalPlaylist],
      currentIndex: this.currentIndex,
      currentTime: this.currentTime,
      duration: this.duration,
      playlistId: this.currentPlaylistId,
      playlistName: this.currentPlaylistName,
      shuffle: this.shuffle,
      repeat: this.repeat,
      isPlaying: this.isPlaying,
      timestamp: Date.now()
    };

    console.log('Scheduled playlist state saved:', {
      playlistName: this.currentPlaylistName,
      currentIndex: this.currentIndex,
      currentTime: this.currentTime
    });
  }

  restoreScheduledPlaylistState() {
    if (!this.scheduledPlaylistState) {
      console.log('No scheduled playlist state to restore');
      return false;
    }

    const state = this.scheduledPlaylistState;

    // Restore playlist state
    this.playlist = [...state.playlist];
    this.originalPlaylist = [...state.originalPlaylist];
    this.currentIndex = state.currentIndex;
    this.currentPlaylistId = state.playlistId;
    this.currentPlaylistName = state.playlistName;
    this.shuffle = state.shuffle;
    this.repeat = state.repeat;
    this.isScheduledPlaylist = true;
    this.interruptionMode = null;

    console.log('Scheduled playlist state restored:', {
      playlistName: this.currentPlaylistName,
      currentIndex: this.currentIndex,
      resumeTime: state.currentTime
    });

    // Resume playback from where it was paused
    if (state.isPlaying && this.playlist.length > 0) {
      this.play(this.currentIndex);

      // Seek to the saved position after a short delay to ensure audio is loaded
      setTimeout(() => {
        if (state.currentTime > 0 && this.currentSound) {
          this.currentSound.seek(state.currentTime);
          this.currentTime = state.currentTime;
          this.notifyListeners();
        }
      }, 500);
    }

    // Clear the saved state
    this.scheduledPlaylistState = null;
    this.notifyListeners();
    return true;
  }

  clearScheduledPlaylistState() {
    this.scheduledPlaylistState = null;
    this.isScheduledPlaylist = false;
    this.interruptionMode = null;
    console.log('Scheduled playlist state cleared');
  }

  hasScheduledPlaylistState() {
    return this.scheduledPlaylistState !== null;
  }

  getScheduledPlaylistInfo() {
    if (!this.scheduledPlaylistState) return null;

    return {
      playlistName: this.scheduledPlaylistState.playlistName,
      currentTrack: this.scheduledPlaylistState.playlist[this.scheduledPlaylistState.currentIndex],
      currentIndex: this.scheduledPlaylistState.currentIndex,
      totalTracks: this.scheduledPlaylistState.playlist.length,
      currentTime: this.scheduledPlaylistState.currentTime,
      duration: this.scheduledPlaylistState.duration
    };
  }

  isCurrentlyScheduled() {
    return this.isScheduledPlaylist;
  }

  getInterruptionMode() {
    return this.interruptionMode;
  }

  // Method to cleanup all audio on logout
  cleanup() {
    this.stop();
    this.playlist = [];
    this.currentIndex = 0;
    this.currentPlaylistId = null;
    this.currentPlaylistName = null;
    this.storeId = null;
    this.listeners = [];
    this.scheduledPlaylistState = null;
    this.isScheduledPlaylist = false;
    this.interruptionMode = null;
    console.log('Audio service cleaned up');
  }
}

export const audioService = new AudioService();
