const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables FIRST
dotenv.config();

// Debug: Check if environment variables are loaded
console.log('MONGO_URI:', process.env.MONGO_URI);
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Loaded' : 'Not loaded');

const app = express();
const connectDB = require('./config/db');

connectDB();

// CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:3000',
      'https://8f882a83b78e.ngrok-free.app',
      'https://8462a7488179.ngrok-free.app',
    ];

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'ngrok-skip-browser-warning',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
  preflightContinue: false,
  optionsSuccessStatus: 200
}));

// Additional middleware to handle ngrok-specific headers
app.use((req, res, next) => {
  // Log the request for debugging
  console.log(`${req.method} ${req.path} - Origin: ${req.get('Origin')}`);

  // Set additional CORS headers for ngrok
  if (req.get('Origin')) {
    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:3000',
      'https://8f882a83b78e.ngrok-free.app',
      'https://8462a7488179.ngrok-free.app',
    ];

    if (allowedOrigins.includes(req.get('Origin'))) {
      res.header('Access-Control-Allow-Origin', req.get('Origin'));
      res.header('Access-Control-Allow-Credentials', 'true');
    }
  }

  next();
});

app.use(express.json());

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'TrakSong API'
  });
});

// Serve static files (uploaded tracks and reports)
app.use('/uploads', express.static('uploads'));
app.use('/api/reports', express.static('reports')); // Serve reports statically

// Ensure reports directory exists
const fs = require('fs');
const path = require('path');
const reportsDir = path.join(__dirname, 'reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
  console.log('Created reports directory');
}

app.use('/api/auth', require('./routes/auth.routes.js'));
app.use('/api/registration', require('./routes/registration.routes.js'));
app.use('/api/tracks', require('./routes/track.routes.js'));
app.use('/api/playlists', require('./routes/playlist.routes.js'));
app.use('/api/history', require('./routes/history.routes.js'));
app.use('/api/reports', require('./routes/report.routes.js'));
app.use('/api/admin', require('./routes/admin.routes.js'));
app.use('/api/stores', require('./routes/store.routes.js'));
app.use('/api/radio', require('./routes/radio.routes.js'));
app.use('/api/compliance', require('./routes/compliance.routes.js'));
app.use('/api/activity', require('./routes/activity.routes.js'));
app.use('/api/sampra', require('./routes/sampra.routes.js'));
app.use('/api/spotify', require('./routes/spotify.routes.js'));
app.use('/api/samro', require('./routes/samro.routes.js'));
app.use('/api/venue-licenses', require('./routes/venueLicense.routes.js'));
app.use('/api/messages', require('./routes/message.routes.js'));
app.use('/api/events', require('./routes/event.routes.js'));
app.use('/api/account', require('./routes/account.routes.js'));


const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));