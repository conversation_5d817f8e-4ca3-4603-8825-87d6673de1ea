const mongoose = require('mongoose');
const Track = require('./models/Track.model');
require('dotenv').config();

async function fixTrackPaths() {
  try {
    console.log('Starting track path fix script...');
    console.log('Connecting to MongoDB...');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/traksong');
    console.log('Connected to MongoDB successfully!');

    // Find all tracks
    const tracks = await Track.find({});
    console.log(`Found ${tracks.length} tracks`);

    // Check for tracks with .mp4 extensions
    const mp4Tracks = tracks.filter(track => track.filePath && track.filePath.includes('.mp4'));
    console.log(`Found ${mp4Tracks.length} tracks with .mp4 extensions`);

    if (mp4Tracks.length > 0) {
      console.log('Tracks with .mp4 extensions:');
      mp4Tracks.forEach((track, index) => {
        console.log(`${index + 1}. ${track.title} by ${track.artist} - ${track.filePath}`);
      });

      // Fix the file paths by changing .mp4 to .mp3
      console.log('\nFixing file paths...');
      for (const track of mp4Tracks) {
        const oldPath = track.filePath;
        const newPath = oldPath.replace('.mp4', '.mp3');
        
        await Track.findByIdAndUpdate(track._id, { filePath: newPath });
        console.log(`Fixed: ${oldPath} -> ${newPath}`);
      }
      
      console.log(`\nFixed ${mp4Tracks.length} track file paths`);
    } else {
      console.log('No tracks with .mp4 extensions found');
    }

    // Show first 5 tracks for verification
    const updatedTracks = await Track.find({}).limit(5);
    console.log('\nFirst 5 tracks after fix:');
    updatedTracks.forEach((track, index) => {
      console.log(`${index + 1}. ${track.title} by ${track.artist} - ${track.filePath}`);
    });

    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
    console.log('Script completed successfully!');
  } catch (error) {
    console.error('Error occurred:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

fixTrackPaths();
