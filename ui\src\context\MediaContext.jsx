import React, { createContext, useContext, useState, useEffect } from 'react';
import { audioService } from '../services/audioService';
import { radioService } from '../services/radioService';
import activityLogger from '../services/activityLogger';

const MediaContext = createContext();

export const useMedia = () => {
  const context = useContext(MediaContext);
  if (!context) {
    throw new Error('useMedia must be used within a MediaProvider');
  }
  return context;
};

export const MediaProvider = ({ children }) => {
  const [mediaState, setMediaState] = useState({
    // Current mode: 'music' or 'radio'
    currentMode: localStorage.getItem('mediaMode') || 'music',

    // Music player state
    musicState: {
      currentTrack: null,
      isPlaying: false,
      playlist: [],
      currentIndex: 0,
      volume: 0.75,
      progress: 0,
      duration: 0,
      currentTime: 0
    },

    // Radio player state
    radioState: {
      currentStation: null,
      isPlaying: false,
      volume: 75, // Initialize as percentage
      isLoading: false
    },

    // Preview state
    previewState: {
      currentTrack: null,
      isPlaying: false,
      audio: null
    },

    // Global state
    isVisible: false, // Whether the persistent player is visible
    isMinimized: false
  });

  useEffect(() => {
    // Subscribe to audio service events
    const unsubscribeAudio = audioService.subscribe((state) => {
      setMediaState(prev => ({
        ...prev,
        musicState: {
          ...prev.musicState,
          currentTrack: state.currentTrack,
          isPlaying: state.isPlaying,
          playlist: audioService.getPlaylist(),
          currentIndex: audioService.getCurrentIndex(),
          volume: audioService.getVolumePercentage(),
          progress: state.progress || 0,
          currentTime: state.currentTime || 0,
          duration: state.duration || 0,
          playlistInfo: audioService.getCurrentPlaylistInfo()
        }
      }));

      // Audio coordination: if music starts playing, ensure radio is stopped
      if (state.isPlaying && radioService.getIsPlaying()) {
        console.log('MediaContext: Music started playing, force stopping radio');
        radioService.forceStop();
      }
    });

    // Subscribe to radio service events
    const unsubscribeRadio = radioService.subscribe((state) => {
      setMediaState(prev => ({
        ...prev,
        radioState: {
          ...prev.radioState,
          currentStation: state.currentStation,
          isPlaying: state.isPlaying,
          volume: radioService.getVolumePercentage(),
          isLoading: state.isLoading
        }
      }));

      // Audio coordination: if radio starts playing, ensure music is stopped
      if (state.isPlaying && audioService.getIsPlaying()) {
        console.log('MediaContext: Radio started playing, stopping music');
        audioService.stop();
      }
    });

    // Initialize services
    radioService.initialize();

    // Initialize radio volume
    setMediaState(prev => ({
      ...prev,
      radioState: {
        ...prev.radioState,
        volume: radioService.getVolumePercentage()
      }
    }));

    // Restore previous session
    const savedStation = radioService.restoreSession();
    if (savedStation) {
      setMediaState(prev => ({
        ...prev,
        isVisible: true,
        currentMode: 'radio'
      }));
    }

    return () => {
      unsubscribeAudio();
      unsubscribeRadio();
    };
  }, []);

  const switchToMusic = async () => {
    console.log('MediaContext: switchToMusic called', { currentMode: mediaState.currentMode });
    if (mediaState.currentMode === 'music') return;

    // Force stop radio if playing
    if (mediaState.radioState.isPlaying || radioService.getIsPlaying()) {
      console.log('MediaContext: Force stopping radio before switching to music');
      radioService.forceStop();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Try to resume scheduled playlist if available
    try {
      const { scheduleService } = await import('../services/scheduleService');
      await scheduleService.resumeScheduledPlaylist();
    } catch (error) {
      console.error('Failed to resume scheduled playlist:', error);
    }

    // Log mode switch
    activityLogger.logModeSwitch('radio_stream', 'music_player');

    setMediaState(prev => ({
      ...prev,
      currentMode: 'music'
    }));

    localStorage.setItem('mediaMode', 'music');
  };

  const switchToRadio = async () => {
    console.log('MediaContext: switchToRadio called', {
      currentMode: mediaState.currentMode,
      musicIsPlaying: mediaState.musicState.isPlaying
    });
    if (mediaState.currentMode === 'radio') return;

    // Save scheduled playlist state if currently playing a scheduled playlist
    if (audioService.isCurrentlyScheduled() && mediaState.musicState.isPlaying) {
      console.log('MediaContext: Saving scheduled playlist state');
      audioService.saveScheduledPlaylistState();
    }

    // Stop music if playing (not just pause)
    if (mediaState.musicState.isPlaying || audioService.getIsPlaying()) {
      console.log('MediaContext: Stopping music before switching to radio');
      audioService.stop();
      // Give a longer delay to ensure stop completes
      await new Promise(resolve => setTimeout(resolve, 150));
    }

    // Set interruption mode
    audioService.interruptionMode = 'radio';

    // Log mode switch
    activityLogger.logModeSwitch('music_player', 'radio_stream');

    setMediaState(prev => ({
      ...prev,
      currentMode: 'radio'
    }));

    localStorage.setItem('mediaMode', 'radio');
  };

  const showPlayer = () => {
    setMediaState(prev => ({
      ...prev,
      isVisible: true,
      isMinimized: false
    }));
  };

  const hidePlayer = () => {
    setMediaState(prev => ({
      ...prev,
      isVisible: false
    }));
  };

  const minimizePlayer = () => {
    setMediaState(prev => ({
      ...prev,
      isMinimized: true
    }));
  };

  const maximizePlayer = () => {
    setMediaState(prev => ({
      ...prev,
      isMinimized: false
    }));
  };

  const playMusic = async (playlist, trackIndex = 0, playlistInfo = null) => {
    console.log('MediaContext: playMusic called', { playlist, trackIndex, playlistInfo });

    // Force stop radio first (without trying to resume anything)
    if (mediaState.radioState.isPlaying || radioService.getIsPlaying()) {
      console.log('MediaContext: Force stopping radio before playing music');
      radioService.forceStop();
      // Give a longer delay to ensure radio stops completely
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Stop any existing music
    if (mediaState.musicState.isPlaying || audioService.getIsPlaying()) {
      console.log('MediaContext: Stopping existing music');
      audioService.stop();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    await switchToMusic();
    audioService.loadPlaylist(playlist, playlistInfo);
    audioService.play(trackIndex);
    showPlayer();
  };

  const playRadio = async (station) => {
    console.log('MediaContext: playRadio called', { station });

    // Stop any existing music first
    if (mediaState.musicState.isPlaying || audioService.getIsPlaying()) {
      console.log('MediaContext: Stopping music before playing radio');
      audioService.stop();
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    await switchToRadio();
    await radioService.playStation(station);
    showPlayer();
  };

  const getCurrentContent = () => {
    if (mediaState.currentMode === 'music') {
      return {
        type: 'music',
        content: mediaState.musicState.currentTrack,
        isPlaying: mediaState.musicState.isPlaying
      };
    } else {
      return {
        type: 'radio',
        content: mediaState.radioState.currentStation,
        isPlaying: mediaState.radioState.isPlaying
      };
    }
  };

  const isAnythingPlaying = () => {
    return mediaState.musicState.isPlaying || mediaState.radioState.isPlaying;
  };

  const startPreview = (track) => {
    // Stop any existing preview first
    stopPreview();

    // Create new audio element for preview
    const audio = new Audio();
    audio.volume = 0.3; // Lower volume for preview
    audio.preload = 'metadata';

    // Set up event listeners before setting the source
    const handleLoadedMetadata = () => {
      // Start 30 seconds in, or at the beginning if track is shorter
      const startTime = Math.min(30, audio.duration * 0.3);
      audio.currentTime = startTime;
    };

    const handleCanPlay = () => {
      setMediaState(prev => ({
        ...prev,
        previewState: {
          ...prev.previewState,
          isPlaying: true
        }
      }));
      audio.play().catch(error => {
        console.error('Failed to play track preview:', error);
        stopPreview();
      });
    };

    const handleEnded = () => {
      stopPreview();
    };

    const handleError = (e) => {
      console.error('Failed to load track preview:', e);
      stopPreview();
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    // Update state with new audio element and track
    setMediaState(prev => ({
      ...prev,
      previewState: {
        currentTrack: track,
        isPlaying: false, // Will be set to true when audio starts playing
        audio: audio
      }
    }));

    // Set the source and start loading
    // Use environment variable for API base URL or default to localhost
    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';
    const filename = track.filePath.split('/').pop();
    audio.src = `${baseUrl}/audio/${filename}`;
  };

  const stopPreview = () => {
    setMediaState(prev => {
      if (prev.previewState.audio) {
        // Clean up the audio element
        prev.previewState.audio.pause();
        prev.previewState.audio.src = '';

        // Remove event listeners to prevent memory leaks
        prev.previewState.audio.removeEventListener('loadedmetadata', () => {});
        prev.previewState.audio.removeEventListener('canplay', () => {});
        prev.previewState.audio.removeEventListener('ended', () => {});
        prev.previewState.audio.removeEventListener('error', () => {});
      }

      return {
        ...prev,
        previewState: {
          currentTrack: null,
          isPlaying: false,
          audio: null
        }
      };
    });
  };

  const togglePreview = (track) => {
    if (mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying) {
      stopPreview();
    } else {
      startPreview(track);
    }
  };

  const stopAllAudio = async () => {
    console.log('MediaContext: Stopping all audio sources');

    // Stop music
    if (mediaState.musicState.isPlaying || audioService.getIsPlaying()) {
      audioService.stop();
    }

    // Force stop radio
    if (mediaState.radioState.isPlaying || radioService.getIsPlaying()) {
      radioService.forceStop();
    }

    // Stop preview
    if (mediaState.previewState.isPlaying) {
      stopPreview();
    }

    // Give a longer delay to ensure everything stops
    await new Promise(resolve => setTimeout(resolve, 200));
  };

  const value = {
    mediaState,
    switchToMusic,
    switchToRadio,
    showPlayer,
    hidePlayer,
    minimizePlayer,
    maximizePlayer,
    playMusic,
    playRadio,
    getCurrentContent,
    isAnythingPlaying,
    startPreview,
    stopPreview,
    togglePreview,
    stopAllAudio
  };

  return (
    <MediaContext.Provider value={value}>
      {children}
    </MediaContext.Provider>
  );
};
