import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  },
  withCredentials: true
});

// Create a separate instance for report exports to avoid double /api in URL
const reportExportApi = axios.create({
  baseURL: API_BASE_URL.replace('/api', ''), // Remove /api from base URL for exports
  withCredentials: true,
  responseType: 'blob'
});

// Add token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Only redirect to login if it's not a login request and we get 401
    if (error.response?.status === 401 && !error.config?.url?.includes('/auth/login')) {
      localStorage.removeItem('token');
      // Only redirect if we're not already on the login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Auth services
export const authService = {
  login: (credentials) => api.post('/auth/login', credentials),
};

// Track services
export const trackService = {
  getAll: (params = {}) => api.get('/tracks', { params }),
  upload: (formData) => api.post('/tracks/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  getDataQualityReport: () => api.get('/tracks/data-quality'),
  fixDataQualityIssues: (issueType, trackIds, fixData) => api.post('/tracks/fix-data-quality', {
    issueType,
    trackIds,
    fixData
  }),
};

// Compliance services
export const complianceService = {
  getDashboard: () => api.get('/compliance/dashboard'),
  generateReport: (data) => api.post('/compliance/reports', data),
  getReports: (params) => api.get('/compliance/reports', { params }),
  getReport: (id) => api.get(`/compliance/reports/${id}`),
  verifyReport: (id, data) => api.put(`/compliance/reports/${id}/verify`, data),
  exportReport: (id, format) => {
    console.log(`API: Exporting report ${id} in ${format} format`);
    return api.get(`/compliance/reports/${id}/export`, {
      params: { format },
      // Excel format returns file directly, others return JSON with download URL
      responseType: format === 'excel' ? 'blob' : 'json'
    });
  },
  getRealTimeMonitoring: () => api.get('/compliance/monitoring/realtime'),
  getSAMROReports: (params) => api.get('/compliance/samro/reports', { params }),
  getSAMPRAReports: (params) => api.get('/compliance/sampra/reports', { params }),

  // Analytics endpoints
  getTopTracks: (params) => api.get('/compliance/analytics/top-tracks', { params }),
  getUsageByRightsHolder: (params) => api.get('/compliance/analytics/rights-holders', { params }),
  getGeographicalData: (params) => api.get('/compliance/analytics/geographical', { params }),
  getComplianceOverview: (params) => api.get('/compliance/analytics/overview', { params }),
  getPlaysByTimeRange: (params) => api.get('/compliance/analytics/plays-by-time', { params }),
  getGenreAnalytics: (params) => api.get('/compliance/analytics/genre-analytics', { params }),
  getComplianceMetrics: (params) => api.get('/compliance/analytics/compliance-metrics', { params }),

  // Filter options endpoints
  getAvailableStores: () => api.get('/compliance/filter-options/stores'),
  getAvailableArtists: () => api.get('/compliance/filter-options/artists'),
  getAvailableComposers: () => api.get('/compliance/filter-options/composers'),
  getAvailablePublishers: () => api.get('/compliance/filter-options/publishers'),
  getAvailableRecordLabels: () => api.get('/compliance/filter-options/record-labels'),

  // Audit trail endpoint
  getAuditLogs: (params) => api.get('/admin/audit-logs', { params }),

  // Bulk operations
  bulkExportReports: (reportIds, formats) => {
    console.log(`API: Bulk exporting ${reportIds.length} reports in formats:`, formats);
    return api.post('/compliance/reports/bulk-export', { reportIds, formats });
  },

  // New compliance validation endpoints
  validateTrackCompliance: (trackId) => api.post(`/compliance/tracks/${trackId}/validate`),
  getRealTimeComplianceStatus: () => api.get('/compliance/monitoring/status'),
  generateMissingMetadataAlerts: () => api.post('/compliance/alerts/generate-missing-metadata'),
  runBatchValidation: (params) => api.post('/compliance/validation/batch', null, { params }),
  startRealTimeMonitoring: () => api.post('/compliance/monitoring/start'),
  stopRealTimeMonitoring: () => api.post('/compliance/monitoring/stop'),

  // SAMRO-specific reporting
  generateSAMROMonthlyReturn: (data) => api.post('/compliance/samro/reports/monthly-return', data),
  exportSAMROReportXML: (params) => reportExportApi.get('/api/samro/export', { params }),

  // SAMPRA-specific reporting
  generateSAMPRAQuarterlyReport: (data) => api.post('/compliance/sampra/reports/quarterly', data),
  exportSAMPRAReportCSV: (reportData) => api.post('/compliance/sampra/reports/export-csv', { reportData }),

  // Compliance alerts
  getAlerts: (params) => api.get('/compliance/alerts', { params }),
  acknowledgeAlert: (id) => api.post(`/compliance/alerts/${id}/acknowledge`),
  resolveAlert: (id) => api.post(`/compliance/alerts/${id}/resolve`),

  // Debug endpoints
  getPlayHistoryDebug: () => api.get('/compliance/debug/play-history'),
  seedData: () => api.post('/compliance/seed-data'),

  // License management
  getLicenses: (params) => api.get('/compliance/licenses', { params }),
  createLicense: (data) => api.post('/compliance/licenses', data),
  updateLicense: (id, data) => api.put(`/compliance/licenses/${id}`, data),
  getLicense: (id) => api.get(`/compliance/licenses/${id}`),

  // Activity monitoring methods (missing methods for MaterialComplianceActivityMonitor)
  getAllActivities: (params) => api.get('/compliance/activities', { params }),
  getActivitySummary: (params) => api.get('/compliance/activities/summary', { params }),
  markActivitiesAsReported: (activityIds, organization) => api.post('/compliance/activities/mark-reported', { activityIds, organization }),
};

// SAMRO services
export const samroService = {
  getDashboard: () => api.get('/samro/dashboard'),
  getRates: (params) => api.get('/samro/rates', { params }),
  createRate: (data) => api.post('/samro/rates', data),
  updateRate: (id, data) => api.put(`/samro/rates/${id}`, data),
  deleteRate: (id) => api.delete(`/samro/rates/${id}`),
  getAlerts: (params) => api.get('/samro/alerts', { params }),
  acknowledgeAlert: (id) => api.post(`/samro/alerts/${id}/acknowledge`),
  resolveAlert: (id) => api.post(`/samro/alerts/${id}/resolve`),
  generateMonthlyReturn: (data) => api.post('/samro/reports/monthly-return', data),
  exportReportXML: (reportData) => api.post('/samro/reports/export-xml', { reportData })
};

// SAMPRA services
export const sampraService = {
  getDashboard: () => api.get('/sampra/dashboard'),
  getRates: (params) => api.get('/sampra/rates', { params }),
  createRate: (data) => api.post('/sampra/rates', data),
  updateRate: (id, data) => api.put(`/sampra/rates/${id}`, data),
  deleteRate: (id) => api.delete(`/sampra/rates/${id}`),
  getAlerts: (params) => api.get('/sampra/alerts', { params }),
  acknowledgeAlert: (id) => api.post(`/sampra/alerts/${id}/acknowledge`),
  resolveAlert: (id) => api.post(`/sampra/alerts/${id}/resolve`),
  generateQuarterlyReport: (data) => api.post('/sampra/reports/quarterly', data),
  exportReportCSV: (reportData) => api.post('/sampra/reports/export-csv', { reportData })
};

// Playlist services
export const playlistService = {
  getAll: () => api.get('/playlists'), // For admin use - gets all playlists
  getByStore: (storeId) => api.get(`/playlists/store/${storeId}`),
  getById: (id) => api.get(`/playlists/${id}`),
  create: (playlist) => api.post('/playlists', playlist),
  update: (id, playlist) => api.put(`/playlists/${id}`, playlist),
  delete: (id) => api.delete(`/playlists/${id}`),
  reorder: (id, orderedTrackIds) => api.put(`/playlists/${id}/reorder`, { orderedTrackIds }),
  getActive: (storeId) => api.get(`/playlists/${storeId}/active`),
};

// History services
export const historyService = {
  log: (playData) => api.post('/history/log', playData),
  get: (params) => api.get('/history', { params }),
  syncOffline: (logs) => api.post('/history/sync-offline', logs),

  // Activity logging for compliance
  logActivity: (activityData) => api.post('/history/activity/log', activityData),
  getActivities: (params) => api.get('/history/activity/all', { params }),
  syncOfflineActivities: (logs) => api.post('/history/activity/sync-offline', logs),
  getSourceSwitchingStats: (storeId, params) => api.get(`/history/activity/switching-stats/${storeId}`, { params }),
};

// Admin services
export const adminService = {
  getTopTracks: (params = {}) => api.get('/admin/analytics/top-tracks', { params }),
  getStoreSummary: (params = {}) => api.get('/admin/analytics/store-summary', { params }),
  getTrendingData: (timeRange = 'week') => api.get('/admin/analytics/trending', { params: { timeRange } }),
  getSystemMetrics: () => api.get('/admin/analytics/system-metrics'),
  getStoreConnectivity: () => api.get('/admin/analytics/store-connectivity'),
  getRealtimeMetrics: () => api.get('/admin/analytics/realtime-metrics'),

  // Advanced analytics endpoints
  getPlaysByTimeRange: (timeRange = 'week') => api.get('/admin/analytics/plays-by-time', { params: { timeRange } }),
  getGenreAnalytics: (timeRange = 'week') => api.get('/admin/analytics/genre-analytics', { params: { timeRange } }),
  getStorePerformance: (timeRange = 'week') => api.get('/admin/analytics/store-performance', { params: { timeRange } }),
  getTrackPopularity: (timeRange = 'week') => api.get('/admin/analytics/track-popularity', { params: { timeRange } }),
  getUserEngagement: (timeRange = 'week') => api.get('/admin/analytics/user-engagement', { params: { timeRange } }),

  // Store management
  getAllStores: () => api.get('/admin/stores'),
  createStore: (storeData) => api.post('/admin/stores', storeData),
  updateStore: (id, storeData) => api.put(`/admin/stores/${id}`, storeData),
  deleteStore: (id) => api.delete(`/admin/stores/${id}`),

  // User management
  getAllUsers: () => api.get('/admin/users'),
  getAllUsersIncludingCompliance: () => api.get('/admin/users/all'),
  createUser: (userData) => api.post('/admin/users', userData),
  updateUser: (id, userData) => api.put(`/admin/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  verifyUser: (id) => api.put(`/admin/users/${id}/verify`),
  suspendUser: (id) => api.put(`/admin/users/${id}/suspend`),
  getRoles: () => Promise.resolve({ data: [
    { value: 'admin', label: 'Administrator' },
    { value: 'store', label: 'Store User' },
    { value: 'artist', label: 'Artist' },
    { value: 'label', label: 'Label' },
    { value: 'samro_staff', label: 'SAMRO Staff' },
    { value: 'sampra_staff', label: 'SAMPRA Staff' },
    { value: 'compliance_admin', label: 'Compliance Admin' }
  ]}),
  getPermissions: () => Promise.resolve({ data: [
    'view_all_reports',
    'export_compliance_data',
    'audit_trail_access',
    'real_time_monitoring',
    'data_verification',
    'cross_organization_access',
    'create_licenses',
    'edit_licenses',
    'delete_licenses',
    'manage_catalogue',
    'upload_tracks',
    'edit_metadata',
    'view_analytics',
    'export_analytics',
    'manage_collaborators',
    'submit_to_stores',
    'manage_licensing',
    'view_royalties',
    'manage_campaigns'
  ]}),



  // Track management
  getAllTracks: () => api.get('/admin/tracks'),
  getTrackDetails: (id, params = {}) => api.get(`/admin/tracks/${id}/details`, { params }),
  generateTrackReport: (id, params = {}) => api.get(`/admin/tracks/${id}/report`, { params }),
  updateTrack: (id, trackData) => api.put(`/admin/tracks/${id}`, trackData),
  deleteTrack: (id) => api.delete(`/admin/tracks/${id}`),

  // Monitoring and logs
  getAuditLogs: (params) => api.get('/admin/audit-logs', { params }),

  // Notifications
  getNotifications: (params) => api.get('/admin/notifications', { params }),
  createNotification: (notificationData) => api.post('/admin/notifications', notificationData),
  updateNotification: (id, notificationData) => api.put(`/admin/notifications/${id}`, notificationData),
  deleteNotification: (id) => api.delete(`/admin/notifications/${id}`),
  markNotificationAsRead: (id) => api.put(`/admin/notifications/${id}/read`),

  // Settings
  getSystemSettings: () => api.get('/admin/settings'),
  updateSystemSettings: (settings) => api.put('/admin/settings', settings),
};

// Store services
export const storeService = {
  getAll: () => api.get('/stores'),
  getById: (id) => api.get(`/stores/${id}`),
  getAnalytics: (storeId, timeRange = 'week') => api.get(`/stores/${storeId}/analytics`, { params: { timeRange } }),
  getTopTracks: (storeId, limit = 10) => api.get(`/stores/${storeId}/top-tracks`, { params: { limit } }),
  getPlaysByHour: (storeId, timeRange = 'week') => api.get(`/stores/${storeId}/plays-by-hour`, { params: { timeRange } }),
};

// Radio services
export const radioService = {
  getStations: () => api.get('/radio/stations'),
  addStation: (stationData) => api.post('/radio/stations', stationData),
  updateStation: (id, stationData) => api.put(`/radio/stations/${id}`, stationData),
  deleteStation: (id) => api.delete(`/radio/stations/${id}`),
  getStoreStations: (storeId) => api.get(`/radio/stations/store/${storeId}`),
  setStoreStations: (storeId, stationIds) => api.put(`/radio/stations/store/${storeId}`, { stationIds }),
};

// Report services
export const reportService = {
  getDailyReport: (params) => api.get('/reports/daily', { params }),
  exportCSV: (params) => reportExportApi.get('/api/reports/daily/csv', { params }),
  exportPDF: (params) => reportExportApi.get('/api/reports/daily/pdf', { params }),
};

// Registration services
export const registrationService = {
  registerStore: (storeData) => api.post('/registration/store', storeData),
  verifyStoreRegistration: (verificationData) => api.post('/registration/store/verify', verificationData),
  resendVerificationCode: (emailData) => api.post('/registration/store/resend-code', emailData),
  checkLicenseAvailability: (licenseNumber, licenseType) =>
    api.get('/registration/check-license', { params: { licenseNumber, licenseType } }),


};

// Venue License services
export const venueLicenseService = {
  getVenueLicenses: (params) => api.get('/venue-licenses', { params }),
  getVenueLicenseById: (id) => api.get(`/venue-licenses/${id}`),
  createVenueLicense: (licenseData) => api.post('/venue-licenses', licenseData),
  updateVenueLicense: (id, licenseData) => api.put(`/venue-licenses/${id}`, licenseData),
  deleteVenueLicense: (id) => api.delete(`/venue-licenses/${id}`),
  getVenueLicenseStats: () => api.get('/venue-licenses/stats'),
  getLicensesByStore: (storeId) => api.get(`/venue-licenses/store/${storeId}`),
};

// Message services
export const messageService = {
  sendMessage: (messageData) => {
    // Handle both regular JSON and FormData
    const config = {};
    if (messageData instanceof FormData) {
      config.headers = {
        'Content-Type': 'multipart/form-data'
      };
    }
    return api.post('/messages/send', messageData, config);
  },
  getMessages: (params) => api.get('/messages', { params }),
  getMessage: (id) => api.get(`/messages/${id}`),
  markAsRead: (id) => api.put(`/messages/${id}/read`),
  deleteMessage: (id) => api.delete(`/messages/${id}`),
  getConversations: () => api.get('/messages/conversations'),
  getUsersForMessaging: (role) => api.get('/messages/users', { params: { role } }),
  downloadAttachment: (messageId, filename) => api.get(`/messages/${messageId}/attachments/${filename}`, {
    responseType: 'blob'
  })
};

// Event services
export const eventService = {
  getAllEvents: (params) => api.get('/events', { params }),
  getEventById: (id) => api.get(`/events/${id}`),
  createEvent: (eventData) => api.post('/events', eventData),
  updateEvent: (id, eventData) => api.put(`/events/${id}`, eventData),
  deleteEvent: (id) => api.delete(`/events/${id}`),
  startEvent: (id) => api.put(`/events/${id}/start`),
  completeEvent: (id, notes) => api.put(`/events/${id}/complete`, { notes }),
  getUpcomingEvents: (limit) => api.get('/events/upcoming', { params: { limit } }),
  getStoreEvents: (storeId, params) => api.get(`/events/store/${storeId}`, { params })
};

export default api;
