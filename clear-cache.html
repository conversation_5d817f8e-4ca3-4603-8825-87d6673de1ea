<!DOCTYPE html>
<html>
<head>
    <title>Clear TrakSong Cache</title>
</head>
<body>
    <h1>Clear TrakSong Cache</h1>
    <button onclick="clearCache()">Clear All Cache</button>
    <div id="output"></div>

    <script>
        function clearCache() {
            const output = document.getElementById('output');
            
            // List all localStorage keys
            const keys = Object.keys(localStorage);
            output.innerHTML = '<h3>Found localStorage keys:</h3>';
            keys.forEach(key => {
                output.innerHTML += `<p>${key}</p>`;
            });
            
            // Clear specific TrakSong cache keys
            const keysToRemove = [
                'offlinePlaylist',
                'offlineTracks', 
                'offlineUserData',
                'pendingPlayLogs',
                'offlineLogs',
                'storeVolume'
            ];
            
            output.innerHTML += '<h3>Clearing cache...</h3>';
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    output.innerHTML += `<p>Removed: ${key}</p>`;
                } else {
                    output.innerHTML += `<p>Not found: ${key}</p>`;
                }
            });
            
            // Also clear any store-specific cache
            keys.forEach(key => {
                if (key.startsWith('store_')) {
                    localStorage.removeItem(key);
                    output.innerHTML += `<p>Removed: ${key}</p>`;
                }
            });
            
            output.innerHTML += '<h3>Cache cleared! Please refresh the TrakSong application.</h3>';
        }
    </script>
</body>
</html>
