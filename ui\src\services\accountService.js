import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  },
  withCredentials: true
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const accountService = {
  // Get current user profile
  getProfile: async () => {
    try {
      const response = await api.get('/account/profile');
      return response;
    } catch (error) {
      console.error('Failed to get profile:', error);
      throw error;
    }
  },

  // Update user profile
  updateProfile: async (profileData) => {
    try {
      const response = await api.put('/account/profile', profileData);
      return response;
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await api.put('/account/password', passwordData);
      return response;
    } catch (error) {
      console.error('Failed to change password:', error);
      throw error;
    }
  },

  // Get account activity
  getActivity: async () => {
    try {
      const response = await api.get('/account/activity');
      return response;
    } catch (error) {
      console.error('Failed to get account activity:', error);
      // Return empty array if activity endpoint fails
      return { data: [] };
    }
  },

  // Get store information (for store users)
  getStoreInfo: async () => {
    try {
      const response = await api.get('/account/store');
      return response;
    } catch (error) {
      console.error('Failed to get store info:', error);
      throw error;
    }
  },

  // Update store information (for store users)
  updateStoreInfo: async (storeData) => {
    try {
      const response = await api.put('/account/store', storeData);
      return response;
    } catch (error) {
      console.error('Failed to update store info:', error);
      throw error;
    }
  },

  // Update account settings
  updateSettings: async (settings) => {
    try {
      const response = await api.put('/account/settings', settings);
      return response;
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  },

  // Get account settings
  getSettings: async () => {
    try {
      const response = await api.get('/account/settings');
      return response;
    } catch (error) {
      console.error('Failed to get settings:', error);
      // Return default settings if endpoint fails
      return { 
        data: {
          emailNotifications: true,
          pushNotifications: true,
          theme: 'light',
          language: 'en'
        }
      };
    }
  },

  // Request email verification
  requestEmailVerification: async () => {
    try {
      const response = await api.post('/account/verify-email');
      return response;
    } catch (error) {
      console.error('Failed to request email verification:', error);
      throw error;
    }
  },

  // Verify email with token
  verifyEmail: async (token) => {
    try {
      const response = await api.post('/account/verify-email/confirm', { token });
      return response;
    } catch (error) {
      console.error('Failed to verify email:', error);
      throw error;
    }
  }
};

export default accountService;
